
import React from 'react';
import { SerialProvider } from '@/context/SerialContext';
import AppSidebar from '@/components/AppSidebar';
import ForceVisualizationContainer from '@/components/ForceVisualizationContainer';
import ForcePlots from '@/components/ForcePlots';
import { SidebarProvider } from "@/components/ui/sidebar";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import { useSerial } from '@/context/SerialContext';
import { useResponsiveBreakpoints } from '@/hooks/use-window-size';
import { usePanelSizes } from '@/hooks/use-panel-sizes';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Dashboard = () => {
  const { forcePlates } = useSerial();
  const { isMobile, isTablet } = useResponsiveBreakpoints();
  const { panelSizes, savePanelSizes } = usePanelSizes();
  
  return (
    <div className="container mx-auto px-4 py-6">
      {forcePlates.length === 0 ? (
        <div className="text-center py-16">
          <h2 className="text-2xl font-bold mb-2">No Force Plates Connected</h2>
          <p className="text-muted-foreground mb-8">
            Connect a force plate to get started with visualization
          </p>
          <div className="bg-card border border-border p-8 rounded-lg max-w-lg mx-auto">
            <h3 className="text-xl font-medium mb-4">Getting Started</h3>
            <ol className="list-decimal list-inside space-y-3 text-left">
              <li>Connect your force plate device to your computer</li>
              <li>Click the "Connect Force Plate" button in the sidebar</li>
              <li>Select the appropriate port from the list</li>
              <li>Click "Start" to begin receiving and visualizing data</li>
            </ol>
          </div>
        </div>
      ) : (
        <div className="h-full">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">Force Plate Visualization</h2>
            <Badge variant="outline" className="text-sm">
              {forcePlates.filter(p => p.isReading).length} Active • {forcePlates.length} Connected
            </Badge>
          </div>

          <ResizablePanelGroup
            direction="vertical"
            className="min-h-[calc(100vh-200px)]"
            onLayout={(sizes) => {
              savePanelSizes({
                visualization: sizes[0],
                plots: sizes[1],
              });
            }}
          >
            <ResizablePanel
              defaultSize={isMobile ? 50 : panelSizes.visualization}
              minSize={isMobile ? 25 : 30}
            >
              <Card className="overflow-hidden h-full">
                <CardHeader className="bg-muted/30 py-3">
                  <CardTitle className={`${isMobile ? 'text-sm' : 'text-md'} flex justify-between`}>
                    <span>3D Force Visualization</span>
                    {!isMobile && (
                      <Badge variant="outline">Hover over plates in sidebar to highlight</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 h-[calc(100%-60px)]">
                  <ForceVisualizationContainer />
                </CardContent>
              </Card>
            </ResizablePanel>

            <ResizableHandle withHandle />

            <ResizablePanel
              defaultSize={isMobile ? 50 : panelSizes.plots}
              minSize={isMobile ? 25 : 20}
            >
              <div className="h-full">
                <ForcePlots />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      )}
    </div>
  );
};

const Index = () => {
  return (
    <SerialProvider>
      <SidebarProvider>
        <ResponsiveLayout />
      </SidebarProvider>
    </SerialProvider>
  );
};

const ResponsiveLayout = () => {
  const { isMobile } = useResponsiveBreakpoints();
  const { panelSizes, savePanelSizes } = usePanelSizes();

  if (isMobile) {
    return (
      <div className="flex min-h-screen w-full">
        <AppSidebar />
        <div className="flex-1">
          <Dashboard />
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen w-full">
      <ResizablePanelGroup
        direction="horizontal"
        className="min-h-screen"
        onLayout={(sizes) => {
          savePanelSizes({
            sidebar: sizes[0],
          });
        }}
      >
        <ResizablePanel
          defaultSize={panelSizes.sidebar}
          minSize={15}
          maxSize={35}
        >
          <AppSidebar />
        </ResizablePanel>

        <ResizableHandle withHandle />

        <ResizablePanel defaultSize={100 - panelSizes.sidebar} minSize={65}>
          <div className="flex-1 h-full">
            <Dashboard />
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export default Index;
