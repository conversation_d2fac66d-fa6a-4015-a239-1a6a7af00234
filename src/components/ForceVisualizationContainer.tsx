import React, { useState, useEffect } from 'react';
import ForceVisualization from './ForceVisualization';
import { PLATE_HOVER_EVENT } from '@/lib/plateHoverEvent';
import { useWindowSize } from '@/hooks/use-window-size';

// Container component to handle the hover events and window resizing
const ForceVisualizationContainer: React.FC = () => {
  const [hoveredPlateId, setHoveredPlateId] = useState<string | null>(null);
  const windowSize = useWindowSize();

  useEffect(() => {
    // Listen for custom hover events from the sidebar
    const handlePlateHover = (e: Event) => {
      const customEvent = e as CustomEvent;
      setHoveredPlateId(customEvent.detail.plateId);
    };

    document.addEventListener(PLATE_HOVER_EVENT, handlePlateHover);

    return () => {
      document.removeEventListener(PLATE_HOVER_EVENT, handlePlateHover);
    };
  }, []);

  return (
    <div className="w-full h-full" style={{ minHeight: '300px' }}>
      <ForceVisualization
        hoveredPlateId={hoveredPlateId}
        windowSize={windowSize}
      />
    </div>
  );
};

export default ForceVisualizationContainer;
